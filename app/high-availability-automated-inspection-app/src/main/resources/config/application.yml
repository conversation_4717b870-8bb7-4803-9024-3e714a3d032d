server:
  port: 8527

management:
  server:
    port: 9527

spring:
  application:
    name: high-availability-automated-inspection
  messages:
    basename: i18n/messages
  session:
    timeout: 900s
    store-type: hazelcast
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
    hiddenmethod:
      filter:
        enabled: true
    throw-exception-if-no-handler-found: true
  jackson:
    serialization:
      write_dates_as_timestamps: false
  quartz:
    job-store-type: memory
    scheduler-name: inspectionScheduler
    startup-delay: 10s
    overwrite-existing-jobs: true
    properties:
      org.quartz.jobStore.class: org.quartz.impl.jdbcjobstore.JobStoreTX
      org.quartz.jobStore.driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
      org.quartz.jobStore.dataSource: myDS
      org.quartz.dataSource.myDS.provider: hikaricp
      org.quartz.dataSource.myDS.driver: com.mysql.cj.jdbc.Driver
      org.quartz.dataSource.myDS.URL: *****************************************************************************
      org.quartz.dataSource.myDS.user: root
      org.quartz.dataSource.myDS.password: your_password
      org.quartz.threadPool.threadCount: 10
      org.quartz.threadPool.threadPriority: 5
      org.quartz.threadPool.class: org.quartz.simpl.SimpleThreadPool
  cache:
    type: hazelcast

lemon:
  alerting:
    resolver: messageResource
    prefix: HAI
  session:
    session-id:
      strategy: Header
      headerName: token
  dataSources:
    primary:
      type: com.alibaba.druid.pool.DruidDataSource
      driverClassName: com.mysql.cj.jdbc.Driver
      url: *****************************************************************************
      username: root
      password: your_password
  dynamicDataSource:
    enabled: true
    defaultDataSource: primary
  idgen:
    generator: simple
  sql:
    level: DEBUG
  actuator:
    health:
      redis:
        enabled: false
  cache:
    jcache:
      spring:
        cache:
          jcache:
            config: config/ehcache3.xml
            provider: org.ehcache.jsr107.EhcacheCachingProvider

mybatis:
  mapper-locations:
    - classpath*:mapper/*.xml

logging:
  config: classpath:config/logback-spring.xml
  level:
    com.cmpay.hacp.inspection.infrastructure.mapper: debug
hazelcast:
  cluster-name: hzcluster-hacp

mybatis-plus:
  configuration:
    default-enum-type-handler: com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler

# Springdoc OpenAPI configuration
springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
    operations-sorter: alpha
    tags-sorter: alpha
  packages-to-scan: com.cmpay.hacp.inspection.controller
  paths-to-match: /v1/**


hacp:
  web:
    subApp:
      cipherType: INSPECTION_CONFIG
  management:
    discovery:
      enabled: true
      name:
      url:
  emergence:
    kubesphere:
      properties:
        url: 'http://************:31407'

inspection:
  firefly:
    base-url: "http://firefly-server:port"
    auth:
      user-id: "your-user-id"
      user-key: "your-user-key"
      token-cache-minutes: 120
      token-url: "/openability/v1/firefly/getToken"  # 自定义token URL
    indicator-url: "/openability/v1/firefly/busOperation/getIndicatorDataList"