package com.cmpay.hacp.inspection.infrastructure.execution.gateway;

import com.cmpay.hacp.inspection.infrastructure.execution.config.SshProperties;
import com.cmpay.hacp.inspection.domain.execution.model.ScriptExecutionRequest;
import com.cmpay.hacp.inspection.domain.execution.model.ScriptExecutionResult;
import com.cmpay.hacp.inspection.domain.execution.model.SshConnectionConfig;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 真实SSH集成测试
 * <p>
 * 如需启用此测试，请在IDEA Run Configuration中设置VM options：
 * -Dssh.integration.test.enabled=true
 * -Dssh.test.host=your_host
 * -Dssh.test.username=your_user
 * -Dssh.test.password=your_pass
 */
@SpringJUnitConfig(SSHGatewayImplIntegrationTest.TestConfig.class)
@EnabledIfSystemProperty(named = "ssh.integration.test.enabled", matches = "true")
public class SSHGatewayImplIntegrationTest {
    @Autowired
    private SSHGatewayImpl sshGateway;
    private SshConnectionConfig connectionConfig;

    @TestConfiguration
    @EnableConfigurationProperties(SshProperties.class)
    static class TestConfig {
        @Bean
        public SSHGatewayImpl sshGateway(SshProperties sshProperties) {
            return new SSHGatewayImpl(sshProperties);
        }
    }

    @BeforeEach
    void setUp() {
        // 初始化SSH客户端
        sshGateway.afterPropertiesSet();

        // 从系统属性中获取SSH连接配置，提供合理的默认值
        connectionConfig = SshConnectionConfig.builder()
                .host(System.getProperty("ssh.test.host", "127.0.0.1"))
                .port(Integer.parseInt(System.getProperty("ssh.test.port", "2222")))
                .username(System.getProperty("ssh.test.username", "vagrant"))
                .password(System.getProperty("ssh.test.password", "vagrant"))
                .authType(SshConnectionConfig.AuthType.PASSWORD)
                .build();
    }

    @AfterEach
    void tearDown() {
        // 清理资源
        if (sshGateway != null) {
            sshGateway.destroy();
        }
    }

    @Test
    void testExecuteSimpleCommand_Success() {
        // 执行简单命令测试
        ScriptExecutionResult result = sshGateway.executeCommand(connectionConfig, "echo 'Hello SSH World'");

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess(), "Command should execute successfully");
        assertEquals(0, result.getExitCode());
        assertTrue(result.getStdout().contains("Hello SSH World"));
        assertEquals("", result.getStderr());
        assertTrue(result.getExecutionTime() > 0);
    }

    @Test
    void testExecuteSimpleCommand_Failed() {

        // 执行失败的命令测试
         ScriptExecutionResult result = sshGateway.executeCommand(connectionConfig, "invalid_command_that_does_not_exist");

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess(), "Invalid command should fail");
        assertNotEquals(0, result.getExitCode());
        assertTrue(!result.getStderr().isEmpty() || result.getStdout().contains("not found"));
        assertTrue(result.getExecutionTime() > 0);
    }

    @Test
    void testExecuteSystemInfoScript_Shell() {
        // 创建系统信息检查脚本
        ScriptExecutionRequest request = ScriptExecutionRequest.builder()
                .scriptContent("#!/bin/bash\n" +
                        "echo \"System Information:\"\n" +
                        "echo \"Hostname: $(hostname)\"\n" +
                        "echo \"Uptime: $(uptime)\"\n" +
                        "echo \"CPU Info: $(cat /proc/cpuinfo | grep 'model name' | head -1)\"\n" +
                        "echo \"Memory: $(free -h | grep 'Mem:' | awk '{print $2}')\"\n" +
                        "echo \"Disk: $(df -h / | tail -1 | awk '{print $4}')\"\n" +
                        "echo \"JSON Output: {\\\"status\\\": \\\"success\\\", \\\"timestamp\\\": \\\"$(date)\\\"}\"")
                .scriptType(ScriptExecutionRequest.ScriptType.SHELL)
                .timeoutSeconds(30)
                .build();

        // 执行脚本
        ScriptExecutionResult result = sshGateway.executeScript(connectionConfig, request);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess(), "System info script should execute successfully");
        assertEquals(0, result.getExitCode());
        assertTrue(result.getStdout().contains("System Information:"));
        assertTrue(result.getStdout().contains("Hostname:"));
        assertTrue(result.getStdout().contains("JSON Output:"));
        assertTrue(result.getStdout().contains("success"));
        assertEquals("", result.getStderr());
        assertTrue(result.getExecutionTime() > 0);
    }

    @Test
    void testExecuteCpuUsageScript_Python() {
        // 创建CPU使用率检查的Python脚本
        ScriptExecutionRequest request = ScriptExecutionRequest.builder()
                .scriptContent("#!/usr/bin/env python3\n" +
                        "import json\n" +
                        "import os\n" +
                        "import time\n" +
                        "\n" +
                        "def get_cpu_usage():\n" +
                        "    with open('/proc/stat', 'r') as f:\n" +
                        "        line = f.readline()\n" +
                        "    cpu_times = [int(x) for x in line.split()[1:]]\n" +
                        "    idle_time = cpu_times[3]\n" +
                        "    total_time = sum(cpu_times)\n" +
                        "    usage = (total_time - idle_time) / total_time * 100\n" +
                        "    return round(usage, 2)\n" +
                        "\n" +
                        "try:\n" +
                        "    cpu_usage = get_cpu_usage()\n" +
                        "    result = {\n" +
                        "        'cpu_usage': cpu_usage,\n" +
                        "        'status': 'success',\n" +
                        "        'timestamp': time.time()\n" +
                        "    }\n" +
                        "    print(json.dumps(result))\n" +
                        "except Exception as e:\n" +
                        "    error_result = {\n" +
                        "        'error': str(e),\n" +
                        "        'status': 'error'\n" +
                        "    }\n" +
                        "    print(json.dumps(error_result))")
                .scriptType(ScriptExecutionRequest.ScriptType.PYTHON)
                .timeoutSeconds(30)
                .build();

        // 执行脚本
        ScriptExecutionResult result = sshGateway.executeScript(connectionConfig, request);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess(), "CPU usage script should execute successfully");
        assertEquals(0, result.getExitCode());
        assertTrue(result.getStdout().contains("cpu_usage") || result.getStdout().contains("error"));
        assertTrue(result.getStdout().contains("status"));
        assertEquals("", result.getStderr());
        assertTrue(result.getExecutionTime() > 0);
    }

    @Test
    void testExecuteScriptWithEnvironmentVariables() {
        // 创建带环境变量的脚本
        Map<String, String> environment = new HashMap<>();
        environment.put("TEST_VAR", "test_value");
        environment.put("INSPECTION_MODE", "production");

        ScriptExecutionRequest request = ScriptExecutionRequest.builder()
                .scriptContent("#!/bin/bash\n" +
                        "echo \"TEST_VAR: $TEST_VAR\"\n" +
                        "echo \"INSPECTION_MODE: $INSPECTION_MODE\"\n" +
                        "echo \"Working directory: $(pwd)\"\n" +
                        "echo \"Environment test completed\"")
                .scriptType(ScriptExecutionRequest.ScriptType.SHELL)
                .environment(environment)
                .workingDirectory("/tmp")
                .timeoutSeconds(30)
                .build();

        // 执行脚本
        ScriptExecutionResult result = sshGateway.executeScript(connectionConfig, request);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess(), "Environment variables script should execute successfully");
        assertEquals(0, result.getExitCode());
        assertTrue(result.getStdout().contains("TEST_VAR: test_value"));
        assertTrue(result.getStdout().contains("INSPECTION_MODE: production"));
        assertTrue(result.getStdout().contains("/tmp"));
        assertTrue(result.getStdout().contains("Environment test completed"));
        assertEquals("", result.getStderr());
        assertTrue(result.getExecutionTime() > 0);
    }

    @Test
    void testExecuteLongRunningScript() {
        // 创建长时间运行的脚本
        ScriptExecutionRequest request = ScriptExecutionRequest.builder()
                .scriptContent("#!/bin/bash\n" +
                        "echo \"Starting long running task...\"\n" +
                        "for i in {1..5}; do\n" +
                        "    echo \"Step $i/5\"\n" +
                        "    sleep 1\n" +
                        "done\n" +
                        "echo \"Task completed successfully\"")
                .scriptType(ScriptExecutionRequest.ScriptType.SHELL)
                .timeoutSeconds(30)
                .build();

        // 记录开始时间
        long startTime = System.currentTimeMillis();

        // 执行脚本
        ScriptExecutionResult result = sshGateway.executeScript(connectionConfig, request);

        // 记录结束时间
        long endTime = System.currentTimeMillis();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess(), "Long running script should execute successfully");
        assertEquals(0, result.getExitCode());
        assertTrue(result.getStdout().contains("Starting long running task"));
        assertTrue(result.getStdout().contains("Step 1/5"));
        assertTrue(result.getStdout().contains("Step 5/5"));
        assertTrue(result.getStdout().contains("Task completed successfully"));
        assertEquals("", result.getStderr());
        assertTrue(result.getExecutionTime() > 0);
        assertTrue(endTime - startTime >= 5000, "Script should take at least 5 seconds");
    }

    @Test
    void testExecuteScriptWithTimeout() {
        // 创建会超时的脚本
        ScriptExecutionRequest request = ScriptExecutionRequest.builder()
                .scriptContent("#!/bin/bash\n" +
                        "echo \"Starting task that will timeout...\"\n" +
                        "sleep 20\n" +
                        "echo \"This should not be printed\"")
                .scriptType(ScriptExecutionRequest.ScriptType.SHELL)
                .timeoutSeconds(5) // 5秒超时
                .build();

        // 执行脚本
        ScriptExecutionResult result = sshGateway.executeScript(connectionConfig, request);

        // 验证结果
        assertNotNull(result);
        // 超时可能导致脚本失败或返回不完整的结果
        assertTrue(result.getStdout().contains("Starting task that will timeout"));
        assertFalse(result.getStdout().contains("This should not be printed"));
        assertTrue(result.getExecutionTime() > 0);
    }

    @Test
    void testConcurrentScriptExecution() throws Exception {
        // 并发执行多个脚本
        int concurrentCount = 3;
        CountDownLatch latch = new CountDownLatch(concurrentCount);
        CompletableFuture<ScriptExecutionResult>[] futures = new CompletableFuture[concurrentCount];

        for (int i = 0; i < concurrentCount; i++) {
            final int taskId = i + 1;
            futures[i] = CompletableFuture.supplyAsync(() -> {
                try {
                    ScriptExecutionRequest request = ScriptExecutionRequest.builder()
                            .scriptContent("#!/bin/bash\n" +
                                    "echo \"Task " + taskId + " started\"\n" +
                                    "sleep 2\n" +
                                    "echo \"Task " + taskId + " completed\"\n" +
                                    "echo \"Result: {\\\"task_id\\\": " + taskId + ", \\\"status\\\": \\\"success\\\"}\"")
                            .scriptType(ScriptExecutionRequest.ScriptType.SHELL)
                            .timeoutSeconds(30)
                            .build();

                    ScriptExecutionResult result = sshGateway.executeScript(connectionConfig, request);
                    latch.countDown();
                    return result;
                } catch (Exception e) {
                    latch.countDown();
                    throw new RuntimeException(e);
                }
            });
        }

        // 等待所有任务完成
        assertTrue(latch.await(30, TimeUnit.SECONDS), "All tasks should complete within 30 seconds");

        // 验证所有结果
        for (int i = 0; i < concurrentCount; i++) {
            ScriptExecutionResult result = futures[i].get();
            assertNotNull(result);
            assertTrue(result.isSuccess(), "Task " + (i + 1) + " should execute successfully");
            assertEquals(0, result.getExitCode());
            assertTrue(result.getStdout().contains("Task " + (i + 1) + " started"));
            assertTrue(result.getStdout().contains("Task " + (i + 1) + " completed"));
            assertTrue(result.getStdout().contains("task_id\": " + (i + 1)));
            assertEquals("", result.getStderr());
            assertTrue(result.getExecutionTime() > 0);
        }
    }

    @Test
    void testExecuteScriptWithInvalidSyntax() {
        // 创建语法错误的脚本
        ScriptExecutionRequest request = ScriptExecutionRequest.builder()
                .scriptContent("#!/bin/bash\n" +
                        "echo \"Testing invalid syntax\"\n" +
                        "if [ 1 -eq 1 ]; then\n" +
                        "    echo \"This is valid\"\n" +
                        "# Missing fi - invalid syntax\n" +
                        "echo \"This will cause syntax error\"")
                .scriptType(ScriptExecutionRequest.ScriptType.SHELL)
                .timeoutSeconds(30)
                .build();

        // 执行脚本
        ScriptExecutionResult result = sshGateway.executeScript(connectionConfig, request);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess(), "Script with syntax error should fail");
        assertNotEquals(0, result.getExitCode());
        assertTrue(result.getStderr().length() > 0 || result.getStdout().contains("syntax error"));
        assertTrue(result.getExecutionTime() > 0);
    }

    @Test
    void testExecuteScriptWithPrivilegedOperation() {
        // 创建需要特权操作的脚本
        ScriptExecutionRequest request = ScriptExecutionRequest.builder()
                .scriptContent("#!/bin/bash\n" +
                        "echo \"Checking system processes...\"\n" +
                        "ps aux | head -5\n" +
                        "echo \"Checking network connections...\"\n" +
//                        "netstat -tulpn | head -5\n" +
                        "ss -tulpn | head -5\n" +
                        "echo \"System check completed\"")
                .scriptType(ScriptExecutionRequest.ScriptType.SHELL)
                .timeoutSeconds(30)
                .build();

        // 执行脚本
        ScriptExecutionResult result = sshGateway.executeScript(connectionConfig, request);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess(), "Privileged operations script should execute successfully");
        assertEquals(0, result.getExitCode());
        assertTrue(result.getStdout().contains("Checking system processes"));
        assertTrue(result.getStdout().contains("Checking network connections"));
        assertTrue(result.getStdout().contains("System check completed"));
        assertEquals("", result.getStderr());
        assertTrue(result.getExecutionTime() > 0);
    }
}
