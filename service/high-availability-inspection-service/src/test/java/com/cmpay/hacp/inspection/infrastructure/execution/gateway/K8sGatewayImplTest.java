package com.cmpay.hacp.inspection.infrastructure.execution.gateway;

import com.cmpay.hacp.inspection.domain.execution.model.K8sConnectionConfig;
import com.cmpay.hacp.inspection.domain.execution.model.ScriptExecutionRequest;
import com.cmpay.hacp.inspection.domain.execution.model.ScriptExecutionResult;
import com.cmpay.hacp.inspection.infrastructure.execution.config.K8sProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * K8sGatewayImpl 针对 Kubernetes 1.31 的测试
 * 使用 22.0.1-legacy Java Client
 */
@ExtendWith(MockitoExtension.class)
class K8sGatewayImplTest {

    private K8sGatewayImpl k8sGateway;
    private K8sProperties k8sProperties;
    private K8sConnectionConfig connectionConfig;
    private ScriptExecutionRequest shellRequest;
    private ScriptExecutionRequest pythonRequest;

    @BeforeEach
    void setUp() {
        // 设置K8s属性 (针对 K8s 1.31 优化)
        k8sProperties = new K8sProperties();
        k8sProperties.setConnectionTimeout(10000);  // 增加超时时间
        k8sProperties.setExecutionTimeout(60000);   // 增加执行超时时间
        k8sProperties.setDefaultKubeconfigPath("~/.kube/config");
        k8sProperties.setDefaultWorkingDirectory("/tmp");
        k8sProperties.setTempFilePrefix("k8s131_test_script_");
        k8sProperties.setCleanupTempFiles(true);

        k8sGateway = new K8sGatewayImpl(k8sProperties);
        try {
            k8sGateway.afterPropertiesSet();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        // 设置连接配置 (K8s 1.31 环境)
        connectionConfig = K8sConnectionConfig.builder()
                .cluster("host")
                .namespace("default")
                .podName("test-5c985559db-954m4")
                .container("container-pse176")
                .kubeconfigPath("~/.kube/config")
                .context("k8s-1.31-context")
                .workingDirectory("/tmp")
                .build();

        // 设置Shell脚本执行请求 (K8s 1.31 兼容)
        shellRequest = ScriptExecutionRequest.builder()
                .scriptContent("#!/bin/bash\n" +
                        "echo 'Hello from K8s 1.31'\n" +
                        "echo 'Kubernetes Version:' $(kubectl version --client --short 2>/dev/null || echo 'kubectl not available')\n" +
                        "echo '{\"status\": \"success\", \"k8s_version\": \"1.31\"}' | jq . 2>/dev/null || echo '{\"status\": \"success\", \"k8s_version\": \"1.31\"}'")
                .scriptType(ScriptExecutionRequest.ScriptType.SHELL)
                .timeoutSeconds(45)
                .build();

        // 设置Python脚本执行请求 (K8s 1.31 兼容)
        Map<String, String> env = new HashMap<>();
        env.put("PYTHONPATH", "/usr/local/lib/python3.9/site-packages:/usr/local/lib/python3.8/site-packages");
        env.put("K8S_VERSION", "1.31");

        pythonRequest = ScriptExecutionRequest.builder()
                .scriptContent("#!/usr/bin/env python3\n" +
                        "import json\n" +
                        "import os\n" +
                        "import sys\n" +
                        "print('Python version:', sys.version)\n" +
                        "print('K8s version:', os.getenv('K8S_VERSION', 'unknown'))\n" +
                        "result = {\n" +
                        "    'status': 'success',\n" +
                        "    'python_version': sys.version.split()[0],\n" +
                        "    'k8s_version': os.getenv('K8S_VERSION', 'unknown'),\n" +
                        "    'message': 'Hello from K8s 1.31 Python script'\n" +
                        "}\n" +
                        "print(json.dumps(result, indent=2))")
                .scriptType(ScriptExecutionRequest.ScriptType.PYTHON)
                .environment(env)
                .workingDirectory("/tmp")
                .timeoutSeconds(60)
                .build();
    }

    @Test
    void testValidateConnectionConfig_K8s131() {
        // 测试 K8s 1.31 特定的配置验证
        assertDoesNotThrow(() -> {
            k8sGateway.executeCommand(connectionConfig, "echo 'K8s 1.31 test'");
        }, "K8s 1.31 配置验证应该通过");
    }

    @Test
    void testValidateConnectionConfig_WithContext() {
        // 测试带有 context 的配置
        K8sConnectionConfig configWithContext = K8sConnectionConfig.builder()
                .namespace("default")
                .podName("test-5c985559db-954m4")
                .container("container-pse176")
                .context("k8s-1.31-context")
                .build();

        assertDoesNotThrow(() -> {
            k8sGateway.executeCommand(configWithContext, "echo 'test with context'");
        });
    }

    @Test
    void testExecuteShellScript_K8s131() {
        // 测试在 K8s 1.31 环境中执行 Shell 脚本
        // 注意：这个测试需要实际的 K8s 1.31 环境
        assertDoesNotThrow(() -> {
            try {
                ScriptExecutionResult result = k8sGateway.executeScript(connectionConfig, shellRequest);
                // 在实际环境中，这里会有真实的执行结果
                assertNotNull(result);
            } catch (RuntimeException e) {
                // 在没有 K8s 环境时，预期会有连接异常
                assertTrue(e.getMessage().contains("kubernetes") ||
                        e.getMessage().contains("connection") ||
                        e.getMessage().contains("cluster") ||
                        e.getMessage().contains("kubeconfig"));
            }
        });
    }

    @Test
    void testExecutePythonScript_K8s131() {
        // 测试在 K8s 1.31 环境中执行 Python 脚本
        assertDoesNotThrow(() -> {
            try {
                ScriptExecutionResult result = k8sGateway.executeScript(connectionConfig, pythonRequest);
                assertNotNull(result);
            } catch (RuntimeException e) {
                // 在没有 K8s 环境时，预期会有连接异常
                assertTrue(e.getMessage().contains("kubernetes") ||
                        e.getMessage().contains("connection") ||
                        e.getMessage().contains("cluster"));
            }
        });
    }

    @Test
    @EnabledIfSystemProperty(named = "k8s.integration.test", matches = "true")
    void testIntegrationWithRealK8s131Cluster() {
        // 集成测试：需要真实的 K8s 1.31 集群
        // 通过系统属性 -Dk8s.integration.test=true 启用

        try {
            // 执行简单的诊断命令
            ScriptExecutionResult result = k8sGateway.executeCommand(connectionConfig,
                    "echo 'K8s 1.31 Integration Test'; date; hostname");

            assertTrue(result.isSuccess(), "命令应该执行成功");
            assertNotNull(result.getStdout(), "应该有标准输出");
            assertTrue(result.getStdout().contains("K8s 1.31 Integration Test"),
                    "输出应该包含测试标识");

        } catch (Exception e) {
            fail("集成测试失败: " + e.getMessage());
        }
    }

    @Test
    void testTimeoutHandling_K8s131() {
        // 测试 K8s 1.31 环境下的超时处理
        K8sProperties shortTimeoutProperties = new K8sProperties();
        shortTimeoutProperties.setConnectionTimeout(1000);  // 1秒超时
        shortTimeoutProperties.setExecutionTimeout(2000);   // 2秒执行超时

        K8sGatewayImpl shortTimeoutGateway = new K8sGatewayImpl(shortTimeoutProperties);
        try {
            shortTimeoutGateway.afterPropertiesSet();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        // 创建一个会超时的长时间运行脚本
        ScriptExecutionRequest longRunningScript = ScriptExecutionRequest.builder()
                .scriptContent("#!/bin/bash\necho 'Starting long task'; sleep 10; echo 'Task completed'")
                .scriptType(ScriptExecutionRequest.ScriptType.SHELL)
                .timeoutSeconds(1)
                .build();

        assertDoesNotThrow(() -> {
            try {
                ScriptExecutionResult result = shortTimeoutGateway.executeScript(connectionConfig, longRunningScript);
                // 如果能连接到集群，应该会超时
                if (result != null) {
                    assertFalse(result.isSuccess(), "长时间运行的脚本应该超时");
                    assertTrue(result.getErrorMessage().contains("timed out"),
                            "错误信息应该包含超时信息");
                }
            } catch (RuntimeException e) {
                // 连接失败是预期的
                assertTrue(e.getMessage().contains("kubernetes") ||
                        e.getMessage().contains("connection"));
            }
        });
    }

    @Test
    void testErrorHandling_K8s131() {
        // 测试 K8s 1.31 环境下的错误处理

        // 测试无效的命名空间
        K8sConnectionConfig invalidConfig = K8sConnectionConfig.builder()
                .namespace("non-existent-namespace-k8s131")
                .podName("non-existent-pod")
                .build();

        assertDoesNotThrow(() -> {
            try {
                k8sGateway.executeCommand(invalidConfig, "echo 'test'");
            } catch (RuntimeException e) {
                // 预期的错误
                assertTrue(e.getMessage().contains("namespace") ||
                        e.getMessage().contains("pod") ||
                        e.getMessage().contains("connection"));
            }
        });
    }
}
