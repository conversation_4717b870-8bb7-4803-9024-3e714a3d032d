package com.cmpay.hacp.extend.sso.ticket.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 用户同步接口返回对象
 */
@Data
public class AccountInfoFourADTO {

    @ApiModelProperty(value = "主账号Id")
    private String pracctId;
    @ApiModelProperty(value = "主帐号名")
    private String pracctName;
    @ApiModelProperty(value = "真实姓名(查询删除数据时无值)")
    private String realName;
    @ApiModelProperty(value = "密码(查询删除数据时无值)")
    private String password;
    @ApiModelProperty(value = "电子邮箱(查询删除数据时无值)")
    private String email;
    @ApiModelProperty(value = "手机号码(查询删除数据时无值)")
    private String mobile;
    @ApiModelProperty(value = "状态0:启用; 1:禁用; 2:离职(查询删除数据时无值)")
    private Integer state;
    @ApiModelProperty(value = "更新状态时间(查询删除数据时无值)")
    private String stateTime;
    @ApiModelProperty(value = "帐号性别 0：女 1: 男(查询删除数据时无值)")
    private Integer userSex;
    @ApiModelProperty(value = "所属机构id(查询删除数据时无值)")
    private String orgId;
    @ApiModelProperty(value = "锁定类型0:管理员手工锁定; 1:认证锁定; 2:过期锁定; 3:长时间未登录锁定(查询删除数据时无值)")
    private String lockType;

}
