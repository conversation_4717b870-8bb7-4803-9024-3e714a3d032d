package com.cmpay.hacp.extend.cmft.service.impl;

import com.cmft.api.utils.APIUtils;
import com.cmft.api.utils.CoderException;
import com.cmft.api.utils.dto.CmftApiEncryptDTO;
import com.cmft.api.utils.helper.Signature;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.extend.cmft.bo.CmftDispatchConfigBO;
import com.cmpay.hacp.extend.cmft.bo.CmftDispatchStrategyBO;
import com.cmpay.hacp.extend.cmft.client.CmftDispatchClient;
import com.cmpay.hacp.extend.cmft.client.dto.*;
import com.cmpay.hacp.extend.cmft.service.CmftDispatchConfigService;
import com.cmpay.hacp.extend.cmft.service.CmftDispatchService;
import com.cmpay.hacp.utils.JsonUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.page.PageInfo;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class CmftDispatchServiceImpl implements CmftDispatchService {

    @Autowired
    private CmftDispatchClient cmftDispatchClient;

    @Autowired
    private CmftDispatchConfigService cmftDispatchConfigService;

    @Value("${cmft.dispatch.strategy.query.type: }")
    private Integer type;

    @Override
    public PageInfo<CmftDispatchStrategyBO> queryStrategyPage(QueryStrategyListReqDTO reqDTO,String workspaceId, CmftDispatchConfigBO dispatchConfigBO) {
        reqDTO.setType(Optional.ofNullable(reqDTO.getType()).orElse(type));
        CmftDispatchConfigBO config = Optional.ofNullable(dispatchConfigBO).orElse(cmftDispatchConfigService.getInfoByWorkspaceId(workspaceId));
        reqDTO.setProjectId(config.getProjectId());
        CmftApiEncryptDTO encryptDTO = getCmftApiEncryptDTO(config,reqDTO);
        BaseApiDTO<CmftApiDecryptDTO> cmftApiEncryptDTO = cmftDispatchClient.queryStrategyList(config.getProjectId(), encryptDTO);
        Map<String, String> rspMap = getCmftApiDecryptDTO(config, cmftApiEncryptDTO);
        PageInfo<CmftDispatchStrategyBO> result = new PageInfo<>(new ArrayList<>());
        ContentRspDTO dataRspDTO = JsonUtil.strToObject(JsonUtil.objToStr(rspMap), ContentRspDTO.class);
        if(rspMap.containsKey("list")){
            List<Map> dataList = JsonUtil.strToObject(rspMap.get("list"), List.class);
            if(JudgeUtils.isEmpty(dataList)){
                return new PageInfo<>(new ArrayList<>());
            }
            List<CmftDispatchStrategyBO> list = JsonUtil.strToObject(JsonUtil.objToStr(dataList),
                    new TypeReference<List<CmftDispatchStrategyBO>>() {});
            result.setList(list);
        }
        result.setTotal(dataRspDTO.getTotal());
        result.setPageNum(dataRspDTO.getPageNum());
        result.setPageSize(dataRspDTO.getPageSize());
        return result;
    }

    @Override
    public void modifyStrategyStatus(CmftDispatchConfigBO config, String id, Integer status){
        ModifyStrategyDTO dto = new ModifyStrategyDTO();
        dto.setId(id);
        dto.setProjectId(config.getProjectId());
        dto.setStatus(status);
        CmftApiEncryptDTO encryptDTO = getCmftApiEncryptDTO(config,dto);
        BaseApiDTO<CmftApiDecryptDTO> rspDto = cmftDispatchClient.modifyStrategyStatus(dto.getProjectId(), encryptDTO);
        Map<String, String> result = getCmftApiDecryptDTO(config, rspDto);
        log.info("modifyStrategyStatus rsp data : {}" ,result);
    }

    @Override
    public void releaseStrategy(CmftDispatchConfigBO config,String... ids){
        ReleaseStrategyDTO releaseStrategyDTO = new ReleaseStrategyDTO();
        releaseStrategyDTO.setProjectId(config.getProjectId());
        if(null == ids && ids.length > 0){
            BusinessException.throwBusinessException(MsgEnum.CMFT_DISPATCH_IDS_NOT_NULL);
        }
        List<String> idList = Arrays.asList(ids);
        releaseStrategyDTO.setStrategyIdList(idList);
        CmftApiEncryptDTO encryptDTO = getCmftApiEncryptDTO(config,releaseStrategyDTO);
        BaseApiDTO<CmftApiDecryptDTO> rspDto = cmftDispatchClient.releaseStrategy(config.getProjectId(), encryptDTO);
        getCmftApiDecryptDTO(config, rspDto);
    }

    private Map<String, String> getCmftApiDecryptDTO(CmftDispatchConfigBO config,BaseApiDTO<CmftApiDecryptDTO> rspDTO){
        try {
            if(!"200".equals(rspDTO.getCode())){
                log.error("cmft dispatch api msg:{}", rspDTO.getMsg());
                BusinessException.throwBusinessException(MsgEnum.CMFT_DISPATCH_API_ERROR);
            }
            CmftApiDecryptDTO content = rspDTO.getContent();
            Signature signature = new Signature(content.getSignature().getR(), content.getSignature().getS());
            return APIUtils.decrypt(content.getData(),
                    content.getTm(),
                    "1.0",
                    config.getCmftPublicKey(),
                    config.getExternalPrivateKey(),
                    signature);
        } catch (CoderException e) {
            log.error("cmft dispatch decrypt error:", e);
            BusinessException.throwBusinessException(MsgEnum.CMFT_DISPATCH_DECRYPT_ERROR,e);
        }
        return null;
    }

    private CmftApiEncryptDTO getCmftApiEncryptDTO(CmftDispatchConfigBO config, Object obj) {
        if(JudgeUtils.isNull(config)||JudgeUtils.isNull(config.getId())){
            BusinessException.throwBusinessException(MsgEnum.CMFT_DISPATCH_CONFIG_NOT_EXIST);
        }
        Map<String, String> reqMap = JsonUtil.strToObject(JsonUtil.objToStr(obj), new TypeReference<Map<String, String>>() {});
        CmftApiEncryptDTO encryptDTO = null;
        try {
            encryptDTO = APIUtils.encrypt(reqMap, "1.0", config.getCmftPublicKey(), config.getExternalPublicKey(), config.getExternalPrivateKey());
        } catch (CoderException e) {
            log.error("cmft dispatch encrypt error:", e);
            BusinessException.throwBusinessException(MsgEnum.CMFT_DISPATCH_ENCRYPT_ERROR,e);
        }
        return encryptDTO;
    }
}
