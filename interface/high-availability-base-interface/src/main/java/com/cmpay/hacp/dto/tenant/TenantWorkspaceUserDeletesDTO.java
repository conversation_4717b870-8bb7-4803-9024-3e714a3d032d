
package com.cmpay.hacp.dto.tenant;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "批量删除项目成员")
public class TenantWorkspaceUserDeletesDTO {

    /**
     * @Fields ids 主键ID集合
     */
    @ApiModelProperty(value = "主键ID集合", required = true)
    @NotEmpty(message = "HAC00009")
    private List<String> ids;

}
