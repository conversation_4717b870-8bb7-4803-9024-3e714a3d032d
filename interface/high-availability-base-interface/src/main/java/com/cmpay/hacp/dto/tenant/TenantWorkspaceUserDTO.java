
package com.cmpay.hacp.dto.tenant;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "项目成员详情")
public class TenantWorkspaceUserDTO {
    /**
     * @Fields id 主键ID
     */
    @ApiModelProperty(value = "主键ID", required = true, example = "3214565dc433428c84ff434379374157")
    private String id;
    /**
     * @Fields tenantId 项目ID
     */
    @ApiModelProperty(value = "租户ID", required = true, example = "1234565dc433428c84ff434379374157")
    private String tenantId;

    /**
     * @Fields tenantName 租户名称
     */
    @ApiModelProperty(value = "租户名称", required = true, example = "租户名称")
    private String tenantName;
    /**
     * @Fields workspaceId 项目ID
     */
    @ApiModelProperty(value = "项目ID", required = true, example = "1234565dc433428c84ff434379374157")
    private String workspaceId;

    /**
     * @Fields workspaceName 项目名称
     */
    @ApiModelProperty(value = "项目名称", required = true, example = "项目名称")
    private String workspaceName;

    /**
     * @Fields workspaceUserType 项目成员类型(1-项目管理员，0-普通用户)
     */
    @ApiModelProperty(value = "项目成员类型(1-项目管理员，0-普通用户)", required = true, example = "0")
    private String workspaceUserType;

    /**
     * @Fields createUser 创建者
     */
    @ApiModelProperty(value = "创建者", required = true, example = "JK8888")
    private String createUser;

    /**
     * @Fields createTime 创建时间
     */
    @ApiModelProperty(value = "创建时间", required = true, example = "2023-08-01 09:51:39")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * @Fields updateUser 更新者
     */
    @ApiModelProperty(value = "更新者", required = true, example = "JK8888")
    private String updateUser;

    /**
     * @Fields updateTime 更新时间
     */
    @ApiModelProperty(value = "更新时间", required = true, example = "2023-08-01 09:51:39")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * @Fields remarks 备注信息
     */
    @ApiModelProperty(value = "备注信息", required = true, example = "备注信息")
    private String remarks;

    /**
     * @Fields status 删除标记
     */
    @ApiModelProperty(value = "删除标记", required = true, example = "0")
    private String status;
    /**
     * @Fields userId 项目成员ID
     */
    @ApiModelProperty(value = "项目成员ID", required = true, example = "MON000001")
    private String userId;

    /**
     * @Fields userName 项目成员用户名
     */
    @ApiModelProperty(value = "项目成员用户名", required = true, example = "JK8888")
    private String userName;

    /**
     * @Fields fullName 项目成员姓名
     */
    @ApiModelProperty(value = "项目成员姓名", required = true, example = "张三")
    private String fullName;

    /**
     * @Fields email 项目成员邮箱
     */
    @ApiModelProperty(value = "项目成员邮箱", required = true, example = "<EMAIL>")
    private String email;

    /**
     * @Fields mobile 项目成员手机
     */
    @ApiModelProperty(value = "项目成员手机", required = true, example = "18888888888")
    private String mobile;

    /**
     * @Fields userStatus 项目成员手机用户状态
     */
    @ApiModelProperty(value = "项目成员手机用户状态", required = true, example = "ENABLE")
    private String userStatus;

    /**
     * @Fields workspaceRoleIds 项目成员角色ID集合
     */
    @ApiModelProperty(value = "项目成员角色ID集合", required = true)
    private List<String> workspaceRoleIds;
    /**
     * @Fields workspaceRoles 项目角色集合
     */
    private List<TenantWorkspaceRole> workspaceRoles;
}
