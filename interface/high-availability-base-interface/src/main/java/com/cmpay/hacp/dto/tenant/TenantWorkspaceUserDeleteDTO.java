package com.cmpay.hacp.dto.tenant;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "删除项目成员")
public class TenantWorkspaceUserDeleteDTO {

    /**
     * @Fields id 主键ID
     */
    @ApiModelProperty(value = "主键ID", required = true, example = "3214565dc433428c84ff434379374157")
    @NotBlank(message = "HAC00009")
    private String id;

}
