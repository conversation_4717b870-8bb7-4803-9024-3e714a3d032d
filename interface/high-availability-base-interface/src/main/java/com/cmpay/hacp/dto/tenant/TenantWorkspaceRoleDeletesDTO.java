package com.cmpay.hacp.dto.tenant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 批量删除项目角色dto
 *
 * <AUTHOR>
 * @date 2023/08/02
 */
@Data
@ApiModel(description = "批量删除项目角色")
public class TenantWorkspaceRoleDeletesDTO {

    /**
     * @Fields tenantIds 项目角色ID集合
     */
    @ApiModelProperty(value = "项目角色ID集合", required = true)
    @NotEmpty(message = "HAC00021")
    private List<String> workspaceRoleIds;

}
