package com.cmpay.hacp.dto.tenant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 新增项目角色dto
 *
 * <AUTHOR>
 * @date 2023/08/02
 */
@Data
@ApiModel(description = "新增项目角色")
public class TenantWorkspaceRoleAddDTO {
    /**
     * @Fields workspaceRoleId 继承的项目角色ID
     */
    @ApiModelProperty(value = "继承的项目角色ID", required = false, example = "0179035dc433428c84ff434379374157")
    private String workspaceRoleId;
    /**
     * @Fields workspaceRoleName 项目角色名称
     */
    @ApiModelProperty(value = "项目角色名称", required = true, example = "项目角色名称")
    @NotBlank(message = "HAC00015")
    private String workspaceRoleName;

    /**
     * @Fields workspaceRoleType 项目角色类型
     */
    @ApiModelProperty(value = "项目角色类型", required = true, example = "项目角色类型")
    @NotBlank(message = "HAC00016")
    private String workspaceRoleType;

    /**
     * @Fields workspaceId 项目ID
     */
    @ApiModelProperty(value = "项目ID", required = true, example = "项目ID")
    @NotBlank(message = "HAC00006")
    private String workspaceId;

    /**
     * @Fields menuIds 菜单ID集合
     */
    @ApiModelProperty(value = "菜单ID集合", required = false)
    private List<Integer> menuIds;
}
