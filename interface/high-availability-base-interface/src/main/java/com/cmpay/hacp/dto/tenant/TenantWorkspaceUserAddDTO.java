
package com.cmpay.hacp.dto.tenant;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "新增项目成员")
public class TenantWorkspaceUserAddDTO {

    /**
     * @Fields workspaceId 项目ID
     */
    @ApiModelProperty(value = "项目ID", required = true, example = "1234565dc433428c84ff434379374157")
    @NotBlank(message = "HAC00004")
    private String workspaceId;

    /**
     * @Fields userId 项目成员ID
     */
    @ApiModelProperty(value = "项目成员ID", required = true, example = "MON000001")
    @NotBlank(message = "HAC00008")
    private String userId;

    /**
     * @Fields workspaceRoleIds 项目成员角色ID集合
     */
    @ApiModelProperty(value = "项目成员角色ID集合", required = true)
    @NotEmpty(message = "HAC00010")
    private List<String> workspaceRoleIds;

    /**
     * @Fields remarks 备注信息
     */
    @ApiModelProperty(value = "备注信息", required = false, example = "备注信息")
    private String remarks;

}
