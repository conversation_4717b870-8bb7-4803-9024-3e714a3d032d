package com.cmpay.hacp.emergency.controller;

import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.hacp.api.VersionApi;
import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.dto.emergency.EmergencyHostAddReqDTO;
import com.cmpay.hacp.dto.emergency.EmergencyHostPageableReqDTO;
import com.cmpay.hacp.dto.emergency.EmergencyHostUpdateReqDTO;
import com.cmpay.hacp.dto.emergency.ExportHostExcelDTO;
import com.cmpay.hacp.emergency.api.HostApi;
import com.cmpay.hacp.emergency.bo.EmergencyHostBO;
import com.cmpay.hacp.emergency.bo.ImportHistoryFileInfoBO;
import com.cmpay.hacp.emergency.client.dto.HostQueryReqDTO;
import com.cmpay.hacp.emergency.service.EmergencyHostService;
import com.cmpay.hacp.emergency.service.ImportHistoryFileInfoService;
import com.cmpay.hacp.enums.HostOSEnum;
import com.cmpay.hacp.enums.KeyValue;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.excel.handle.sheet.CustomDropDownSheetWriteHandler;
import com.cmpay.hacp.system.log.annotation.LogNoneRecord;
import com.cmpay.hacp.system.log.annotation.LogRecord;
import com.cmpay.hacp.system.service.SystemCipherService;
import com.cmpay.hacp.tenant.utils.TenantSecurityUtils;
import com.cmpay.hacp.tenant.utils.TenantUtils;
import com.cmpay.hacp.utils.JsonUtil;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.hacp.utils.excel.ExportExcel;
import com.cmpay.hacp.utils.excel.ImportExcel;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.security.SecurityUtils;
import com.cmpay.lemon.framework.utils.LemonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 应急机器控制器
 *
 * <AUTHOR>
 * @create 2024/08/21 14:02:05
 * @since 1.0.0
 */

@RestController
@Api(tags = "应急调度主机管理")
@RequestMapping(VersionApi.VERSION_V1+ HostApi.HOST_URL)
@RequiredArgsConstructor
@Slf4j
public class EmergencyHostController {

    /**
     * 应急机器服务
     */
    private final EmergencyHostService emergencyHostService;

    private final ImportHistoryFileInfoService importHistoryFileInfoService;

    private final SystemCipherService systemCipherService;

    /**
     * 新增
     *
     * @param reqDTO req dto
     * @return 成功/失败
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增", notes = "新增")
    @ApiResponse(code = 200, message = "返回结果")
    @LogRecord(title = "新增主机信息", action = "新增")
    @PreAuthorize("hasPermission('EmergencyHostController','emergency:host:add')")
    public DefaultRspDTO<NoBody> add(@Validated @RequestBody EmergencyHostAddReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        emergencyHostService.add(BeanConvertUtil.convert(reqDTO, EmergencyHostBO.class));
        return DefaultRspDTO.newSuccessInstance();
    }


    /**
     * 修改
     *
     * @param reqDTO req dto
     * @return 成功/失败
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改", notes = "修改")
    @ApiResponse(code = 200, message = "返回结果")
    @LogRecord(title = "修改主机信息", action = "修改")
    @PreAuthorize("hasPermission('EmergencyHostController','emergency:host:update')")
    public DefaultRspDTO<NoBody> update(@Validated @RequestBody EmergencyHostUpdateReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        emergencyHostService.update(BeanConvertUtil.convert(reqDTO, EmergencyHostBO.class));
        return DefaultRspDTO.newSuccessInstance();
    }

    /**
     * 删除
     *
     * @param reqDTO req dto
     * @return 成功/失败
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除", notes = "删除")
    @ApiResponse(code = 200, message = "返回结果")
    @LogRecord(title = "删除主机信息", action = "删除")
    @PreAuthorize("hasPermission('EmergencyHostController','emergency:host:delete')")
    public DefaultRspDTO<NoBody> delete(@RequestBody EmergencyHostUpdateReqDTO reqDTO) {
        TenantSecurityUtils.copyWorkspace(reqDTO);
        emergencyHostService.delete(BeanConvertUtil.convert(reqDTO, EmergencyHostBO.class));
        return DefaultRspDTO.newSuccessInstance();
    }

    /**
     * 查询详情
     *
     * @param reqDTO req dto
     * @return 详情
     */
    @GetMapping("/info")
    @ApiOperation(value = "查询详情", notes = "查询详情")
    @ApiResponse(code = 200, message = "详情")
    @LogRecord(title = "查询主机信息", action = "查询")
    @PreAuthorize("hasPermission('EmergencyHostController','emergency:host:query')")
    public DefaultRspDTO<EmergencyHostBO> getInfo(EmergencyHostUpdateReqDTO reqDTO) {
        TenantSecurityUtils.copyWorkspace(reqDTO);
        EmergencyHostBO result = emergencyHostService.getDetailInfo(BeanConvertUtil.convert(reqDTO, EmergencyHostBO.class));
        result.setHostPassword(JudgeUtils.isNotBlank(result.getHostPassword())? CommonConstant.ENCRYPTED_DISPLAY:"");
        return DefaultRspDTO.newSuccessInstance(result);
    }

    /**
     * 查询分页列表
     *
     * @param reqDTO req dto
     * @return 查询分页列表
     */
    @GetMapping("/page")
    @ApiOperation(value = "查询分页列表", notes = "查询分页列表")
    @ApiResponse(code = 200, message = "列表")
    @LogNoneRecord
    @PreAuthorize("hasPermission('EmergencyHostController','emergency:host:query')")
    public DefaultRspDTO<PageInfo<EmergencyHostBO>> getPage(EmergencyHostPageableReqDTO reqDTO) {
        TenantSecurityUtils.copyWorkspace(reqDTO);
        PageInfo<EmergencyHostBO> page = emergencyHostService.getPage(reqDTO.getPageNum(),reqDTO.getPageSize(),BeanConvertUtil.convert(reqDTO, EmergencyHostBO.class));
        return DefaultRspDTO.newSuccessInstance(page);
    }

    /**
     * 查询列表
     *
     * @return 查询列表
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询列表", notes = "查询列表")
    @ApiResponse(code = 200, message = "列表")
    @LogNoneRecord
    public DefaultRspDTO<List<EmergencyHostBO>> getList(@RequestBody EmergencyHostPageableReqDTO reqDTO) {
        TenantSecurityUtils.copyWorkspace(reqDTO);
        List<EmergencyHostBO> list = emergencyHostService.getList(BeanConvertUtil.convert(reqDTO,EmergencyHostBO.class));
        return DefaultRspDTO.newSuccessInstance(list);
    }


    @GetMapping("/drop-down")
    @ApiOperation(value = "查询下拉", notes = "查询下拉")
    @ApiResponse(code = 200, message = "查询下拉")
    @LogNoneRecord
    public DefaultRspDTO<Map<String,List<KeyValue>>> getDropDown(){
        Map<String,List<KeyValue>> result=emergencyHostService.getDropDown();
        return DefaultRspDTO.newSuccessInstance(result);
    }

    @GetMapping("/template")
    @ApiOperation(value = "下载主机导入模板", notes = "下载主机导入模板")
    @ApiResponse(code = 200, message = "下载主机导入模板")
    @LogNoneRecord
    @PreAuthorize("hasPermission('EmergencyHostController','emergency:host:template')")
    public ResponseEntity<byte[]> template(){
        emergencyHostService.template(TenantUtils.getWorkspaceIdNotNull());
        ExportHostExcelDTO exportHostExcelDTO = new ExportHostExcelDTO();
        exportHostExcelDTO.setHostAddress("***********");
        exportHostExcelDTO.setHostPort(22);
        exportHostExcelDTO.setHostUsername("test");
        exportHostExcelDTO.setHostPassword("test");
        exportHostExcelDTO.setHostTag("支付中台;用户中心");
        exportHostExcelDTO.setHostDesc("主机描述");
        exportHostExcelDTO.setHostApp("userBase");
        exportHostExcelDTO.setHostOS(HostOSEnum.Linux);
        return new ExportExcel("", ExportHostExcelDTO.class, new CustomDropDownSheetWriteHandler(ExportHostExcelDTO.class))
                .setDataList(Collections.singletonList(exportHostExcelDTO)).write("主机数据");
    }


    @PostMapping("/import")
    @ApiOperation(value = "导入主机数据", notes = "导入主机数据")
    @ApiResponse(code = 200, message = "导入主机数据")
    @LogRecord(title = "导入主机数据", action = "导入")
    @PreAuthorize("hasPermission('EmergencyHostController','emergency:host:import')")
    public GenericRspDTO<NoBody> importHost(MultipartFile file) {
        List<EmergencyHostBO> emergencyHosts=new ArrayList<>();
        try {
            ImportExcel importExcel = new ImportExcel(file, 0, 0);
            List<ExportHostExcelDTO> dataList = importExcel.getDataList(ExportHostExcelDTO.class);
            emergencyHosts = BeanConvertUtil.convertList(dataList, EmergencyHostBO.class);
        } catch (InvalidFormatException | IOException |InstantiationException | IllegalAccessException e) {
            log.error("importInfo Exception: {}", e.getMessage());
            BusinessException.throwBusinessException(MsgEnum.EXCEL_ANALYSIS_ERROR);
        }
        try {
            emergencyHostService.importHost(emergencyHosts, TenantUtils.getWorkspaceId(), SecurityUtils.getLoginUser());
        } catch (BusinessException e) {
            log.error("importInfo Exception: {}", e.getMessage());
            BusinessException.throwBusinessException(e);
        } finally {
            ImportHistoryFileInfoBO importHistoryFileInfo = new ImportHistoryFileInfoBO();
            TenantSecurityUtils.copyTenantSecurity(importHistoryFileInfo);
            importHistoryFileInfo.setBatchNumber(LemonUtils.getRequestId());
            importHistoryFileInfoService.addImportHistoryFileInfo(importHistoryFileInfo,file,emergencyHosts);
        }
        return GenericRspDTO.newSuccessInstance();
    }

    /**
     * 查询详情
     *
     * @param reqDTO req dto
     * @return 详情
     */
    @PostMapping(HostApi.HOST_SUB_QUERY_URL)
    @ApiOperation(value = "查询主机列表", notes = "查询主机列表")
    @ApiResponse(code = 200, message = "主机列表")
    @LogRecord(title = "查询主机列表信息", action = "查询")
    public DefaultRspDTO<String> queryHostInfoList(HostQueryReqDTO reqDTO) {
        List<EmergencyHostBO> result = emergencyHostService.getList(BeanConvertUtil.convert(reqDTO, EmergencyHostBO.class));
        if(JudgeUtils.isEmpty(result)){
            return null;
        }
        result = result.stream().peek(item -> item.setHostPassword(systemCipherService.otherModuleDecryptData(item.getHostPassword()))).collect(
                Collectors.toList());
        String data = systemCipherService.subEncryptData(reqDTO.getCipherType(), JsonUtil.objToStr(result));
        return DefaultRspDTO.newSuccessInstance(data);
    }
 }
