ARG ARG_REPOSITORY=repos-yltest-test.cloud.test
#
# target app 后台应用
# example: docker build  --target app --build-arg ARG_USER=rise-system-management --build-arg ARG_LEMONBASE_VERSION=1.0.0 --build-arg ARG_VERSION=1.0.1 --build-arg ARG_REPOSITORY=172.16.54.213 --build-arg ARG_APPLICATION=rise-system-management-app --no-cache -t rise-system-management-app .
# -------------

FROM repos-yltest-test.cloud.test/agent/agent_build_java_8:V1 as builder
COPY ./ /home/<USER>/
WORKDIR /home/<USER>/

RUN curl -O http://nexus.devops.cmft/repository/public-tools/devops/gradle8.tar.gz \
    && tar -xzf gradle8.tar.gz \
    && mkdir -p $GRADLE_USER_HOME  \
    && cp gradle.properties $GRADLE_USER_HOME/ \
    && ./gradlew clean bootJar --refresh-dependencies

FROM repos-yltest-test.cloud.test/lemon/rise-centos-7.9.2009-adoptium-jdk8:1.0.0
MAINTAINER tangnianwanggy

# 用户名
ARG ARG_USER=apprun
ARG ARG_GROUP=develop
# 声明业务端口
EXPOSE 8527
# 声明监控端口
EXPOSE 9527
# 声明HAZELCAST集群端口
EXPOSE 5702
# 项目名
ARG ARG_APPLICATION=high-availability-control-platform-app
ARG ARG_VERSION=1.0.2

# 应用版本、玲珑框架基础镜像版本
LABEL version=$ARG_VERSION

RUN mkdir /app \
    && groupadd $ARG_GROUP \
    && useradd -d /app/$ARG_USER -g $ARG_GROUP -m $ARG_USER \
    && mkdir -p  /app/$ARG_USER/lib /app/$ARG_USER/logs  \
    && chown -R $ARG_USER:$ARG_GROUP /app/$ARG_USER
# 环境变量
ENV LEMON_LOGGER_LEVEL=INFO \
    LEMON_ENV=prd \
    LEMON_HOME=/app/$ARG_USER

USER $ARG_USER
# 拷贝jar包
COPY --chown=$ARG_USER:$ARG_GROUP --from=builder /home/<USER>/app/$ARG_APPLICATION/build/libs/*.jar /app/$ARG_USER/lib/

# 工作目录
WORKDIR /app/$ARG_USER

# 启动命令
CMD ["start.sh"]